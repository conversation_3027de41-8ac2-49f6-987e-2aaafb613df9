import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { isDemoMode } from '@/app/config/demoMode';
import { BrokerService } from '@/lib/services/brokerService';
import { UserService } from '@/lib/services/userService';

// Get current broker connection status
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user from database to get the internal user ID
    const dbUser = await UserService.findBySupabaseId(user.id);
    if (!dbUser) {
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    // Get broker connection from database (works for both demo and production)
    const connection = await BrokerService.getBrokerConnection(dbUser.id);
    const demoMode = isDemoMode();

    console.log(`[${demoMode ? 'DEMO' : 'PROD'} MODE] Broker status for user ${dbUser.id}:`, connection);

    return NextResponse.json({
      success: true,
      connection,
      demoMode
    });

  } catch (error) {
    console.error('Error fetching broker status:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch broker status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
