import { prisma } from '@/lib/prisma';
import { ZerodhaCredentials, User } from '@prisma/client';

export interface BrokerConnection {
  id: string;
  brokerId: string;
  brokerName: string;
  userId: string;
  accountId: string;
  connectedAt: string;
  status: 'active' | 'inactive' | 'error';
  accessToken?: string;
  refreshToken?: string;
  isDemo: boolean;
}

export interface BrokerInfo {
  id: string;
  name: string;
  logo: string;
  description: string;
  status: 'available' | 'coming_soon' | 'maintenance';
  authUrl?: string;
}

export class BrokerService {
  // Available brokers configuration
  static readonly AVAILABLE_BROKERS: BrokerInfo[] = [
    {
      id: 'kite',
      name: '<PERSON><PERSON> (Zerodha)',
      logo: '🪁',
      description: 'India\'s largest discount broker with advanced trading platform',
      status: 'available',
      authUrl: 'https://kite.zerodha.com/connect/login'
    },
    {
      id: 'grow',
      name: '<PERSON><PERSON>',
      logo: '🌱',
      description: 'Modern investment platform with smart portfolio management',
      status: 'available',
      authUrl: 'https://grow.com/oauth/authorize'
    },
    {
      id: 'upstox',
      name: 'Upstox',
      logo: '📈',
      description: 'Technology-driven discount broker with powerful trading tools',
      status: 'coming_soon',
      authUrl: 'https://api.upstox.com/v2/login/authorization'
    }
  ];

  // Get all available brokers
  static async getAvailableBrokers(): Promise<BrokerInfo[]> {
    return this.AVAILABLE_BROKERS;
  }

  // Get broker info by ID
  static getBrokerInfo(brokerId: string): BrokerInfo | null {
    return this.AVAILABLE_BROKERS.find(broker => broker.id === brokerId) || null;
  }

  // Connect user to a broker (demo mode)
  static async connectBroker(
    userId: string,
    brokerId: string,
    isDemo: boolean = true,
    authData?: {
      accessToken?: string;
      refreshToken?: string;
      zerodhaUserId?: string;
    }
  ): Promise<BrokerConnection> {
    const brokerInfo = this.getBrokerInfo(brokerId);
    if (!brokerInfo) {
      throw new Error(`Broker ${brokerId} not found`);
    }

    if (brokerInfo.status !== 'available') {
      throw new Error(`Broker ${brokerInfo.name} is currently ${brokerInfo.status}`);
    }

    // Check if user already has a broker connection
    const existingConnection = await this.getBrokerConnection(userId);
    if (existingConnection) {
      // Update existing connection
      await this.disconnectBroker(userId);
    }

    // Generate demo data for demo mode
    const demoData = isDemo ? {
      zerodhaUserId: `DEMO_${brokerId.toUpperCase()}_${userId.slice(-6)}`,
      accessToken: `demo_access_${brokerId}_${Date.now()}`,
      refreshToken: `demo_refresh_${brokerId}_${Date.now()}`
    } : authData;

    // Create or update Zerodha credentials
    const credentials = await prisma.zerodhaCredentials.upsert({
      where: { userId },
      update: {
        zerodhaUserId: demoData?.zerodhaUserId,
        accessToken: demoData?.accessToken,
        refreshToken: demoData?.refreshToken,
        isConnected: true,
        lastSyncAt: new Date(),
        updatedAt: new Date()
      },
      create: {
        userId,
        zerodhaUserId: demoData?.zerodhaUserId,
        accessToken: demoData?.accessToken,
        refreshToken: demoData?.refreshToken,
        isConnected: true,
        lastSyncAt: new Date()
      }
    });

    // Return broker connection info
    return {
      id: credentials.id,
      brokerId,
      brokerName: brokerInfo.name,
      userId,
      accountId: credentials.zerodhaUserId || `${brokerId.toUpperCase()}_${userId.slice(-6)}`,
      connectedAt: credentials.createdAt.toISOString(),
      status: 'active',
      accessToken: credentials.accessToken || undefined,
      refreshToken: credentials.refreshToken || undefined,
      isDemo
    };
  }

  // Get user's broker connection
  static async getBrokerConnection(userId: string): Promise<BrokerConnection | null> {
    const credentials = await prisma.zerodhaCredentials.findUnique({
      where: { userId },
      include: {
        user: true
      }
    });

    if (!credentials || !credentials.isConnected) {
      return null;
    }

    // For now, we assume all connections are Zerodha (Kite)
    // In the future, we can add a brokerId field to the credentials table
    const brokerInfo = this.getBrokerInfo('kite');
    if (!brokerInfo) {
      return null;
    }

    return {
      id: credentials.id,
      brokerId: 'kite',
      brokerName: brokerInfo.name,
      userId,
      accountId: credentials.zerodhaUserId || `KITE_${userId.slice(-6)}`,
      connectedAt: credentials.createdAt.toISOString(),
      status: 'active',
      accessToken: credentials.accessToken || undefined,
      refreshToken: credentials.refreshToken || undefined,
      isDemo: credentials.user.isDemo
    };
  }

  // Disconnect user from broker
  static async disconnectBroker(userId: string): Promise<boolean> {
    try {
      const result = await prisma.zerodhaCredentials.updateMany({
        where: { 
          userId,
          isConnected: true
        },
        data: {
          isConnected: false,
          accessToken: null,
          refreshToken: null,
          updatedAt: new Date()
        }
      });

      return result.count > 0;
    } catch (error) {
      console.error('Error disconnecting broker:', error);
      return false;
    }
  }

  // Update broker credentials
  static async updateBrokerCredentials(
    userId: string,
    data: {
      accessToken?: string;
      refreshToken?: string;
      zerodhaUserId?: string;
      tokenExpiry?: Date;
    }
  ): Promise<ZerodhaCredentials | null> {
    try {
      return await prisma.zerodhaCredentials.update({
        where: { userId },
        data: {
          ...data,
          lastSyncAt: new Date(),
          updatedAt: new Date()
        }
      });
    } catch (error) {
      console.error('Error updating broker credentials:', error);
      return null;
    }
  }

  // Check if user is connected to any broker
  static async isUserConnected(userId: string): Promise<boolean> {
    const connection = await this.getBrokerConnection(userId);
    return connection !== null;
  }

  // Get broker connection status for multiple users
  static async getBrokerConnectionsForUsers(userIds: string[]): Promise<Map<string, BrokerConnection | null>> {
    const credentials = await prisma.zerodhaCredentials.findMany({
      where: {
        userId: { in: userIds },
        isConnected: true
      },
      include: {
        user: true
      }
    });

    const connectionMap = new Map<string, BrokerConnection | null>();
    
    // Initialize all users with null
    userIds.forEach(userId => connectionMap.set(userId, null));

    // Set connections for users who have them
    credentials.forEach(cred => {
      const brokerInfo = this.getBrokerInfo('kite');
      if (brokerInfo) {
        connectionMap.set(cred.userId, {
          id: cred.id,
          brokerId: 'kite',
          brokerName: brokerInfo.name,
          userId: cred.userId,
          accountId: cred.zerodhaUserId || `KITE_${cred.userId.slice(-6)}`,
          connectedAt: cred.createdAt.toISOString(),
          status: 'active',
          isDemo: cred.user.isDemo
        });
      }
    });

    return connectionMap;
  }
}
