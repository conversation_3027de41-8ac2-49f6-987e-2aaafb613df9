import { NextRequest, NextResponse } from 'next/server';
import { isDemoMode } from '@/app/config/demoMode';
import { BrokerService } from '@/lib/services/brokerService';

// Get list of available brokers
export async function GET(request: NextRequest) {
  try {
    // Get brokers from service (same for both demo and production)
    const brokers = await BrokerService.getAvailableBrokers();
    const demoMode = isDemoMode();

    // Add auth URLs for production mode
    const brokersWithAuth = brokers.map(broker => ({
      ...broker,
      authUrl: demoMode
        ? broker.authUrl
        : broker.id === 'kite'
          ? `https://kite.zerodha.com/connect/login?v=3&api_key=${process.env.NEXT_PUBLIC_ZERODHA_API_KEY}`
          : broker.authUrl,
      demoMode
    }));

    return NextResponse.json({
      success: true,
      brokers: brokersWithAuth,
      demoMode
    });

  } catch (error) {
    console.error('Error fetching brokers:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch brokers',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
