import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { isDemoMode } from '@/app/config/demoMode';
import { ChildUserService } from '@/lib/services/childUserService';
import { UserService } from '@/lib/services/userService';

// Get list of connected child users
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user from database to get the internal user ID
    const dbUser = await UserService.findBySupabaseId(user.id);
    if (!dbUser) {
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    const demoMode = isDemoMode();

    // Get child users and pending invitations from database (works for both demo and production)
    const childUsers = await ChildUserService.getChildUsers(dbUser.id);
    const pendingInvitations = await ChildUserService.getPendingInvitations(dbUser.id);

    console.log(`[${demoMode ? 'DEMO' : 'PROD'} MODE] Child users for master ${dbUser.id}:`, {
      childCount: childUsers.length,
      pendingCount: pendingInvitations.length
    });

    return NextResponse.json({
      success: true,
      childUsers,
      pendingInvitations,
      demoMode
    });

  } catch (error) {
    console.error('Error fetching child users:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch child users',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
