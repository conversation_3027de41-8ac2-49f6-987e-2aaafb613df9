import { prisma } from '@/lib/prisma'
import { Invitation, InvitationStatus, User } from '@prisma/client'
import { randomBytes } from 'crypto'

export type InvitationWithUsers = Invitation & {
  sender: User
  receiver?: User | null
}

export interface CreateInvitationData {
  senderEmail: string;
  receiverEmail: string;
  senderId: string;
  type: string;
  expiresInDays: number;
}

export class InvitationService {
  // Create a new invitation
  static async createInvitation(
    data: CreateInvitationData
  ): Promise<Invitation> {
    const token = randomBytes(32).toString('hex')
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + data.expiresInDays)

    return prisma.invitation.create({
      data: {
        senderId: data.senderId,
        receiverEmail: data.receiverEmail,
        token,
        expiresAt,
        status: InvitationStatus.PENDING,
      },
    })
  }

  // Find invitation by token
  static async findByToken(token: string): Promise<InvitationWithUsers | null> {
    return prisma.invitation.findUnique({
      where: { token },
      include: {
        sender: true,
        receiver: true,
      },
    })
  }

  // Get invitations sent by a user
  static async getSentInvitations(senderId: string): Promise<InvitationWithUsers[]> {
    return prisma.invitation.findMany({
      where: { senderId },
      include: {
        sender: true,
        receiver: true,
      },
      orderBy: { createdAt: 'desc' },
    })
  }

  // Get invitations received by email
  static async getReceivedInvitations(receiverEmail: string): Promise<InvitationWithUsers[]> {
    return prisma.invitation.findMany({
      where: { receiverEmail },
      include: {
        sender: true,
        receiver: true,
      },
      orderBy: { createdAt: 'desc' },
    })
  }

  // Accept invitation
  static async acceptInvitation(token: string, receiverId: string): Promise<Invitation> {
    const invitation = await prisma.invitation.findUnique({
      where: { token },
    })

    if (!invitation) {
      throw new Error('Invitation not found')
    }

    if (invitation.status !== InvitationStatus.PENDING) {
      throw new Error('Invitation is not pending')
    }

    if (invitation.expiresAt < new Date()) {
      throw new Error('Invitation has expired')
    }

    return prisma.invitation.update({
      where: { token },
      data: {
        status: InvitationStatus.ACCEPTED,
        receiverId,
        acceptedAt: new Date(),
      },
    })
  }

  // Cancel invitation
  static async cancelInvitation(token: string, senderId: string): Promise<Invitation> {
    const invitation = await prisma.invitation.findUnique({
      where: { token },
    })

    if (!invitation) {
      throw new Error('Invitation not found')
    }

    if (invitation.senderId !== senderId) {
      throw new Error('Unauthorized to cancel this invitation')
    }

    if (invitation.status !== InvitationStatus.PENDING) {
      throw new Error('Can only cancel pending invitations')
    }

    return prisma.invitation.update({
      where: { token },
      data: {
        status: InvitationStatus.CANCELLED,
      },
    })
  }

  // Mark expired invitations
  static async markExpiredInvitations(): Promise<number> {
    const result = await prisma.invitation.updateMany({
      where: {
        status: InvitationStatus.PENDING,
        expiresAt: {
          lt: new Date(),
        },
      },
      data: {
        status: InvitationStatus.EXPIRED,
      },
    })

    return result.count
  }

  // Check if invitation exists for email
  static async invitationExists(senderId: string, receiverEmail: string): Promise<boolean> {
    const invitation = await prisma.invitation.findFirst({
      where: {
        senderId,
        receiverEmail,
        status: InvitationStatus.PENDING,
        expiresAt: {
          gt: new Date(),
        },
      },
    })

    return !!invitation
  }

  // Get invitation statistics for a sender
  static async getInvitationStats(senderId: string): Promise<{
    total: number
    pending: number
    accepted: number
    expired: number
    cancelled: number
  }> {
    const invitations = await prisma.invitation.findMany({
      where: { senderId },
    })

    return {
      total: invitations.length,
      pending: invitations.filter(inv => inv.status === InvitationStatus.PENDING).length,
      accepted: invitations.filter(inv => inv.status === InvitationStatus.ACCEPTED).length,
      expired: invitations.filter(inv => inv.status === InvitationStatus.EXPIRED).length,
      cancelled: invitations.filter(inv => inv.status === InvitationStatus.CANCELLED).length,
    }
  }

  // Validate invitation token
  static async validateInvitation(token: string): Promise<{
    valid: boolean
    invitation?: InvitationWithUsers
    error?: string
  }> {
    const invitation = await this.findByToken(token)

    if (!invitation) {
      return { valid: false, error: 'Invitation not found' }
    }

    if (invitation.status !== InvitationStatus.PENDING) {
      return { valid: false, error: 'Invitation is not pending' }
    }

    if (invitation.expiresAt < new Date()) {
      return { valid: false, error: 'Invitation has expired' }
    }

    return { valid: true, invitation }
  }
}
