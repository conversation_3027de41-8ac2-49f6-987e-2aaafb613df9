import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { isDemoMode } from '@/app/config/demoMode';
import { BrokerService } from '@/lib/services/brokerService';
import { UserService } from '@/lib/services/userService';

// Handle broker connection
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { brokerId, authCode } = await request.json();

    if (!brokerId) {
      return NextResponse.json(
        { error: 'Broker ID is required' },
        { status: 400 }
      );
    }

    // Get user from database to get the internal user ID
    const dbUser = await UserService.findBySupabaseId(user.id);
    if (!dbUser) {
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    const demoMode = isDemoMode();

    // Connect to broker using database service (works for both demo and production)
    const connection = await BrokerService.connectBroker(
      dbUser.id,
      brokerId,
      demoMode,
      authCode ? { accessToken: authCode } : undefined
    );

    console.log(`[${demoMode ? 'DEMO' : 'PROD'} MODE] Successfully connected user ${dbUser.id} to broker ${brokerId}:`, connection);

    return NextResponse.json({
      success: true,
      connection,
      message: `Successfully connected to ${connection.brokerName}${demoMode ? ' (Demo Mode)' : ''}`,
      demoMode
    });



  } catch (error) {
    console.error('Error connecting to broker:', error);
    return NextResponse.json(
      {
        error: 'Failed to connect to broker',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
