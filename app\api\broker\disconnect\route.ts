import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { isDemoMode } from '@/app/config/demoMode';
import { BrokerService } from '@/lib/services/brokerService';
import { UserService } from '@/lib/services/userService';

// Handle broker disconnection
export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user from database to get the internal user ID
    const dbUser = await UserService.findBySupabaseId(user.id);
    if (!dbUser) {
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    const demoMode = isDemoMode();

    // Disconnect from broker using database service (works for both demo and production)
    const success = await BrokerService.disconnectBroker(dbUser.id);

    console.log(`[${demoMode ? 'DEMO' : 'PROD'} MODE] Disconnected user ${dbUser.id} from broker:`, success);

    return NextResponse.json({
      success,
      message: success
        ? `Successfully disconnected from broker${demoMode ? ' (Demo Mode)' : ''}`
        : 'No broker connection found',
      demoMode
    });

  } catch (error) {
    console.error('Error disconnecting from broker:', error);
    return NextResponse.json(
      {
        error: 'Failed to disconnect from broker',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
